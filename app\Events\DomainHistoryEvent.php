<?php

namespace App\Events;

use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class DomainHistoryEvent
{
    use Dispatchable, SerializesModels;

    public $domainId;
    public $type;
    public $userId;
    public $status;
    public $message;
    public $payload;
    /**
     * Create a new event instance.
     *
     * @param array $data
     * @return void
     */
    public function __construct(array $data)
    {
        $this->domainId = $data['domain_id'];
        $this->type = $data['type'];
        $this->userId = $data['user_id'];
        $this->status = $data['status'] ?? 'success';
        $this->message = $data['message'] ?? null;
        $this->payload = $data['payload'] ?? null;
    }
}
