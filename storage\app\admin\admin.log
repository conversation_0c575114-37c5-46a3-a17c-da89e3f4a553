[2025-08-29 02:15:00] local.INFO: ApprovalDeleteRequest: Running...  
[2025-08-29 02:15:02] local.INFO: ApprovalDeleteRequest: Processed 1 expired requests  
[2025-08-29 02:15:02] local.INFO: ApprovalDeleteRequest: Done  
[2025-08-29 02:17:23] local.ERROR: {"query":{"statusType":"APPROVED"},"parameter":{"statusType":"APPROVED"},"error":"ParseError","message":"syntax error, unexpected token \",\", expecting identifier or variable or \"{\" or \"$\"","code":0}  
[2025-08-29 02:44:02] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:45:28] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:47:11] local.ERROR: {"query":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:15] local.ERROR: {"query":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"requested_at:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:18] local.ERROR: {"query":{"statusType":"DELETED","orderby":"email:desc","email":null},"parameter":{"statusType":"DELETED","orderby":"email:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The selected orderby is invalid. (and 1 more error)","code":0}  
[2025-08-29 02:47:21] local.ERROR: {"query":{"statusType":"DELETED","orderby":"domain:asc","email":null},"parameter":{"statusType":"DELETED","orderby":"domain:asc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:26] local.ERROR: {"query":{"statusType":"ALL","orderby":"domain:desc","email":null},"parameter":{"statusType":"ALL","orderby":"domain:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:29] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 02:47:32] local.ERROR: {"query":{"statusType":"ALL","orderby":"domain:desc","email":null},"parameter":{"statusType":"ALL","orderby":"domain:desc","email":null},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:47:39] local.ERROR: {"query":{"statusType":"ALL","email":"asd"},"parameter":{"statusType":"ALL","email":"asd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:59:00] local.ERROR: {"query":{"orderby":"requested_at:desc"},"parameter":{"orderby":"requested_at:desc"},"error":"Illuminate\\Database\\QueryException","message":"SQLSTATE[42703]: Undefined column: 7 ERROR:  column domain_cancellation_requests.created_at does not exist
LINE 1: ...n_id\" = \"registered_domains\".\"domain_id\" order by \"domain_ca...
                                                             ^ (Connection: client, SQL: select \"domains\".\"name\" as \"domainName\", \"domains\".\"status\", \"domains\".\"deleted_at\" as \"domainDeletedAt\", \"domains\".\"created_at\", \"users\".\"id\" as \"user_id\", \"users\".\"email\", \"users\".\"first_name\", \"users\".\"last_name\", \"domain_cancellation_requests\".\"deleted_at\", \"domain_cancellation_requests\".\"requested_at\", \"domain_cancellation_requests\".\"reason\", \"domain_cancellation_requests\".\"id\" as \"dcrID\", \"domain_cancellation_requests\".\"support_agent_id\", \"domain_cancellation_requests\".\"support_agent_name\", \"domain_cancellation_requests\".\"support_note\", \"domain_cancellation_requests\".\"feedback_date\", \"domain_cancellation_requests\".\"domain_id\", \"registered_domains\".\"status\" as \"rstatus\" from \"domain_cancellation_requests\" inner join \"domains\" on \"domain_cancellation_requests\".\"domain_id\" = \"domains\".\"id\" inner join \"users\" on \"domain_cancellation_requests\".\"user_id\" = \"users\".\"id\" inner join \"registered_domains\" on \"domain_cancellation_requests\".\"domain_id\" = \"registered_domains\".\"domain_id\" order by \"domain_cancellation_requests\".\"created_at\" desc limit 20 offset 0)","code":"42703"}  
[2025-08-29 02:59:32] local.ERROR: {"query":{"email":"asd"},"parameter":{"email":"asd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 02:59:36] local.ERROR: {"query":{"email":"zcasd"},"parameter":{"email":"zcasd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 03:02:43] local.ERROR: {"query":{"email":"asdd"},"parameter":{"email":"asdd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 03:02:51] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-08-29 03:02:57] local.ERROR: {"query":{"email":"asdasd"},"parameter":{"email":"asdasd"},"error":"Illuminate\\Validation\\ValidationException","message":"The email field must be a valid email address.","code":0}  
[2025-08-29 06:47:34] local.ERROR: {"query":[],"parameter":[],"error":"Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException","message":"The route .well-known\/appspecific\/com.chrome.devtools.json could not be found.","code":0}  
[2025-09-01 08:14:28] local.INFO: user login from 127.0.0.1  
[2025-09-01 09:23:33] local.INFO: user login from 127.0.0.1  
[2025-09-02 01:16:00] local.ERROR: {"query":[],"parameter":[],"error":"Illuminate\\Auth\\AuthenticationException","message":"Unauthenticated.","code":0}  
[2025-09-02 01:18:21] local.INFO: user login from 127.0.0.1  
[2025-09-02 02:02:23] local.ERROR: {"query":[],"parameter":{"domainName":"limittestme.net","userEmail":"<EMAIL>","domainId":112,"createdDate":"2025-08-19 09:42:03","userID":7,"support_note":"test"},"error":"Illuminate\\Validation\\ValidationException","message":"The support note field must be at least 10 characters.","code":0}  
[2025-09-02 02:02:28] local.INFO: Domain History: Domain deletion request cancelled by admin 1 (a@a.a)  
