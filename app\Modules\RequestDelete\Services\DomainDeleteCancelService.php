<?php

namespace App\Modules\RequestDelete\Services;

use App\Mail\UserDeleteRequestMail;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use App\Events\DomainHistoryEvent;

class DomainDeleteCancelService
{
    private Carbon $now;

    public function __construct()
    {
        $this->now = Carbon::now();
    }

    public static function instance()
    {
        return new self;
    }

    public function cancelDeleteRequestSave($requestOrData)
    {
        $data = is_array($requestOrData) ? $requestOrData : $requestOrData->all();

        if (empty($data['support_note'])) {
            throw new \Exception("Support note is required when cancelling a domain deletion request.");
        }

        $this->processCancelRequest($data);
    }

    private function processCancelRequest(array $data): void
    {
        $adminId = Auth::id();
        $adminName = Auth::user()->name ?? 'System';
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $supportNote = $data['support_note'] ?? "Request delete cancelled by {$adminEmail}";

        $domainInfo = $this->getDomainInfo($data['domainId']);
        if (!$domainInfo) {
            throw new \Exception("Domain deletion request not found for domain ID: {$data['domainId']}");
        }

        $data['domainName'] = $domainInfo->domainName;
        $data['userID'] = $domainInfo->userID;
        $data['userEmail'] = $domainInfo->userEmail;

        event(new DomainHistoryEvent(['domain_id' => $data['domainId'],'type' => 'DOMAIN_CANCELLED','status' => 'success','user_id' => $data['userID'],'message' => 'Domain deletion request cancelled by ' . $adminName . ' (' . $adminEmail . ')','payload' => json_encode($data),]));

        $this->cancelDomainDeletionRequest($data, $adminId, $adminName, $supportNote);

        $this->userNotification($data);
        $this->userEmailNotification($data);
    }

    private function userNotification($requestData)
    {
        $userID = $requestData['userID'];
        $domainName = $requestData['domainName'];

        if (!$userID || !$domainName) return;

        $message = 'Your domain deletion request for "' . $domainName . '" has been cancelled. You can contact support if you have any questions.';

        DB::client()->table('notifications')->insert([
            'user_id'      => $userID,
            'title'        => 'Domain Deletion Request Cancelled',
            'message'      => $message,
            'redirect_url' => '/domain',
            'created_at'   => now(),
            'updated_at'   => now(),
            'importance'   => 'important',
        ]);
    }

    private function userEmailNotification($requestData)
    {
        $domainName = $requestData['domainName'];
        $userEmail = $requestData['userEmail'];

        $body = 'Your domain deletion request for "' . $domainName . '" has been cancelled. The domain will not be deleted. You can contact support if you have any questions.';

        $message = [
            'subject'  => 'Domain Deletion Request Cancelled',
            'greeting' => 'Greetings!',
            'body'     => $body,
            'text'     => Carbon::now()->format('Y-m-d H:i:s'),
            'sender'   => 'StrangeDomains Support',
        ];

        Mail::to($userEmail)->send(new UserDeleteRequestMail($message));

        $this->emailTrack($userEmail, $message, $requestData['domainId']);
    }

    private function getUserByDomain($domainId) 
    {
        return DB::client()->table('registered_domains')
            ->select('users.id as user_id', 'users.email', 'users.first_name', 'users.last_name')
            ->join('user_contacts', 'user_contacts.id', '=', 'registered_domains.user_contact_registrar_id')
            ->join('users', 'users.id', '=', 'user_contacts.user_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->where('domains.id', $domainId)
            ->first();
    }

    private function emailTrack($email, array $payload, $domainId = null) 
    {
        $userId = null;
        $userName = null;

        if ($domainId) {
            $user = $this->getUserByDomain($domainId);
            if ($user) {
                $userId = $user->user_id;
                $userName = $user->first_name . ' ' . $user->last_name;
            }
        }

        $emailSent = DB::client()->table('email_histories')
            ->insert([
                'user_id' => $userId,
                'name' => $userName ?? 'System',
                'recipient_email' => $email,
                'subject' => 'Domain Deletion Request Cancelled',
                'email_type' => 'Domain Deletion Request Cancelled',
                'email_body' => json_encode($payload),
                'attachment' => null,
                'created_at' => now(),
                'updated_at' => now()
            ]);

        return $emailSent;
    }

    private function getDomainInfo($domainId)
    {
        return DB::client()->table('domain_cancellation_requests')
            ->join('domains', 'domain_cancellation_requests.domain_id', '=', 'domains.id')
            ->join('users', 'domain_cancellation_requests.user_id', '=', 'users.id')
            ->where('domain_cancellation_requests.domain_id', $domainId)
            ->select([
                'domains.name as domainName',
                'domain_cancellation_requests.user_id as userID',
                'users.email as userEmail',
                'domain_cancellation_requests.reason'
            ])
            ->first();
    }

    private function cancelDomainDeletionRequest(array $data, int $adminId, string $adminName, string $supportNote): void
    {
        $adminEmail = Auth::user()->email ?? '<EMAIL>';
        $adminFullName = "{$adminName} ({$adminEmail})";

        DB::client()->table('domain_cancellation_requests')
            ->where('domain_id', $data['domainId'])
            ->update([
                'support_agent_id'   => $adminId,
                'support_agent_name' => $adminFullName,
                'feedback_date'      => null,
                'support_note'       => $supportNote,
                'deleted_at'         => now(),
            ]);

        $this->reactivateDomain($data['domainId']);
    }

    private function reactivateDomain(int $domainId): void
    {
        $timestamp = now();

        DB::client()->table('domains')
            ->where('id', $domainId)
            ->update([
                'status' => 'ACTIVE',
                'updated_at' => $timestamp,
            ]);

        DB::client()->table('registered_domains')
            ->where('domain_id', $domainId)
            ->update([
                'status' => 'OWNED',
                'updated_at' => $timestamp,
            ]);
    }
}
